<template>
  <main id="dealRecord">
    <header>
      <van-nav-bar :title="$t('main.dealRecord.label1')" left-arrow @click-left="$router.back()" />
      <div class="right">
        <van-dropdown-menu active-color="#CF866B">
          <van-dropdown-item v-model="params.type" :options="typeList" @change="changeType">
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </header>

    <nav>
      <div class="block" :class="{ active: params.by === 'self' }" @click="changeBy('self')">{{ $t('main.dealRecord.label2') }}</div>
      <div class="block" :class="{ active: params.by === 'son' }" @click="changeBy('son')">{{ $t('main.dealRecord.label3') }}</div>
    </nav>


    <div class="dateGroup">
      <div class="input" :class="{ placeholder: !params.start_time }" @click="startTimeShow = true">
        <span>{{ params.start_time || startPlaceholder }}</span>
        <div class="close" @click.stop="params.start_time = undefined">
          <van-icon name="cross" size="11px"/>
        </div>
      </div>
      <div class="input" :class="{ placeholder: !params.end_time }" @click="endTimeShow = true">
        <span>{{ params.end_time || endPlaceholder }}</span>
        <div class="close" @click.stop="params.end_time = undefined">
          <van-icon name="cross" size="11px"/>
        </div>
      </div>
    </div>

    <div class="list">
      <van-list v-model="loading" :finished="finished" offset="50" @load="onLoad" class="tbody" :loading-text="`${ $t('foot.loading') }...`" :finished-text="`${ $t('main.lottery.label16') }`">
        <div class="tr" v-for="(item, index) in dealRecordList" :key="index" >
          <div class="top">
            <div class="name">{{ item.remark }}</div>
            <div class="price">
              <span>{{ $t('main.dealRecord.label4') }}：</span>
              <span>{{ item.money }}</span>
            </div>
          </div>
          <div class="bottom">
            <div class="time">{{ item.created_at }}</div>
            <div class="balance">{{ $t('main.dealRecord.label5') }}：{{ item.money_after }}</div>
          </div>
        </div>
      </van-list>
    </div>

    <van-popup v-model="startTimeShow" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        :title="$t('main.dealRecord.label8')"
        :min-date="minDate"
        :max-date="maxDate"
        :confirm-button-text="$t('main.dealRecord.label6')"
        :cancel-button-text="$t('main.dealRecord.label7')"
        @confirm="selectStartDate"
      />
    </van-popup>


    <van-popup v-model="endTimeShow" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        :title="$t('main.dealRecord.label9')"
        :min-date="minDate"
        :max-date="maxDate"
        :confirm-button-text="$t('main.dealRecord.label6')"
        :cancel-button-text="$t('main.dealRecord.label7')"
        @confirm="selectEndDate"
      />
    </van-popup>


  </main>
</template>
<script>
export default {
  name: 'message',
  data() {
    return {
      params: {
        start_time: '',
        end_time: '',
        by: 'self',
        type: '',
        page: 0
      },
      startPlaceholder: '',
      endPlaceholder: '',
      typeList: [],
      currentDate: new Date(),
      minDate: new Date(2020,0,1),
      maxDate: new Date(2030,0,1),
      startTimeShow: false,
      endTimeShow: false,
      loading: false,
      finished: false,
      dealRecordList: [],
      dealRecordData: {},
    }
  },
  created() {
    this.startPlaceholder = this.$t('main.dealRecord.label8')
    this.endPlaceholder = this.$t('main.dealRecord.label9')

    this.$parent.loading = true
    this.getDealRecordType()
    this.params.start_time = this.formatDate(new Date(), 'start')
    this.params.end_time = this.formatDate(new Date(), 'end')
    // this.getDealRecordList()
    this.getDate()
  },
  methods: {
    async getDealRecordType() {
      const res = await this.$apiFun.getDealRecordTypeApi()
      console.log(res)
      const list = [{text: this.$t('main.dealRecord.label10'), value: ''}]
      for(let i in res.data) {
        list.push({
          text: res.data[i],
          value: i
        })
      }
      this.typeList = list
    },


    async getDealRecordList(refresh = false) {
      const res = await this.$apiFun.getDealRecordListApi(this.params)
      // 判断是刷新还是加载
      if (refresh) {
        this.dealRecordList = res.data.data
      } else {
        this.dealRecordList = [...this.dealRecordList, ...res.data.data]
      }
      this.dealRecordData = res.data
      this.loading = false
      if (this.params.page >= this.dealRecordData.last_page) {
        this.finished = true
      }
      this.$parent.loading = false
    },


    onLoad () {
      this.loading = true
      this.params.page++
      this.getDealRecordList(false)
    },

    getDate() {
      const date = new Date()
    },

    changeBy(e) {
      this.params.by = e
      this.params.page = 1
      this.$parent.loading = true
        this.finished = false
        this.loading = true
      this.getDealRecordList(true)
    },

    changeType(e) {
      this.params.type = e
      this.params.page = 1
      this.$parent.loading = true
        this.finished = false
        this.loading = true
      this.getDealRecordList(true)
      console.log(e)
    },

    async selectStartDate(e) {
      if(this.params.end_time) {
        const decide = await this.decideTime(this.formatDate(e), this.params.end_time)
        if(decide) {
          return
        }
      }
      this.params.start_time = this.formatDate(e)
      this.startTimeShow = false
      if(this.params.start_time && this.params.end_time) {
        this.params.page = 1
        this.$parent.loading = true
        this.finished = false
        this.loading = true
        this.getDealRecordList(true)
      }
    },
    async selectEndDate(e) {
      if(this.params.start_time) {
        const decide = await this.decideTime(this.params.start_time, this.formatDate(e))
        if(decide) {
          return
        }
      }
      this.params.end_time = this.formatDate(e)
      this.endTimeShow = false
      if(this.params.start_time && this.params.end_time) {
        this.params.page = 1
        this.$parent.loading = true
        this.finished = false
        this.loading = true
        this.getDealRecordList(true)
      }
    },

    decideTime(start_time, end_time) {
      const startDate = new Date(start_time)
      const endDate = new Date(end_time)
      if(startDate.getMonth() !== endDate.getMonth() || startDate.getFullYear() !== endDate.getFullYear()) {
        this.$parent.showTost(0, this.$t('main.dealRecord.toast1'))
        return true
      }
      else if (Date.parse(start_time) > Date.parse(end_time)) {
        this.$parent.showTost(0, this.$t('main.dealRecord.toast2'))
        return true
      }
      return false
    },

    formatDate(e, type) {
      const n = '-'
      const date = new Date(e)
      const year = date.getFullYear()
      const month = date.getMonth() + 1 < 10 ? `0${Number(date.getMonth()) + 1}` : Number(date.getMonth()) + 1
      const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()
      let hour = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()
      let minute = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()
      if(type == 'start') {
        hour = '00'
        minute = '00'
      }else if(type == 'end') {
        hour = '23'
        minute = '59'
      }
      const r = `${year}${n}${month}${n}${day} ${hour}:${minute}`
      return r
    },
// selectEndDate
  },
  mounted() {
    let that = this;
  },
  updated() {
    let that = this;
  },
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
header {
  height: 60px;
  .van-nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .right {
    position: fixed;
    display: flex;
    align-items: center;
    z-index: 1000;
    height: 46px;
    line-height: 46px;
    right: 20px;
    font-weight: 500;
    font-size: .25rem;
  }
}

nav {
  width: calc(100% - 30px);
  height: 30px;
  border: 1px solid #cf866b;
  box-sizing: border-box;
  margin: 0 15px;
  display: flex;
  .block {
    flex: 1;
    text-align: center;
    line-height: 28px;
    color: #cf866b;
  }
  .active {
    background: #cf866b;
    color: #fff;
  }
}

.dateGroup {
  display: flex;
  margin: 0 15px;
  margin-top: 10px;
  gap: 15px;
  .input {
    border: 1px solid #ccc;
    flex: 1;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    position: relative;
  }
  .placeholder {
    color: #bbb;
  }
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    height: 16px;
    width: 16px;
    background: #ccc;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: .35rem;
  }
}


.list {
  box-sizing: border-box;
  height: calc(var(--vh) * 100 - 90px - 47px);
  padding-top: 10px;
  overflow: auto;
  .tr {
    height: 40px;
    margin: 0px 15px;
    padding: 5px 5px 5px;
    border-bottom: 1px solid #eee;
    >div {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
  }
  .price {
    color: #cf866b;
  }
  .bottom {
    div {
      color: #999
    }
  }
}

// ::v-deep .van-nav-bar__title {
//   font-size: 2rem !important;
// }


</style>


<style lang="scss">

.van-dropdown-menu__bar {
  height: 17px !important;
  box-shadow: none !important;
  background: none !important;
}

.van-dropdown-item {
  top: 46px !important;
  .van-cell__title span {
    font-size: .34rem !important;
  }
}

.van-dropdown-menu {
  height: 17px !important;

  .van-ellipsis {
    font-size: .4rem;
    font-weight: 500;
  }
}

.van-dropdown-menu__title:after {
  border-color: transparent transparent #333 #333;
}

.van-dropdown-menu__title--active:after {
  border-color: transparent transparent currentColor currentColor;
}

#dealRecord {
  .van-ellipsis {
    font-size: .3rem;
  }
  .van-nav-bar__title {
    font-size: .4rem;
  }
}
</style>
