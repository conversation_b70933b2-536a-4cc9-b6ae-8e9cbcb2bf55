<template>
  <main>
    <header>
      <van-nav-bar :title="$t('main.agent.label1')" left-arrow @click-left="$router.back()" />
    </header>

    <div class="nav">
      <div class="block" :class="{ active: navActive === 0 }" @click="changeNav(0)">
        <div class="label">{{ $t('main.agent.label2') }}</div>
      </div>
      <div class="block" :class="{ active: navActive === 1 }" @click="changeNav(1)">
        <div class="label">{{ $t('main.agent.label3') }}</div>
      </div>
      <div class="block" :class="{ active: navActive === 2 }" @click="changeNav(2)">
        <div class="label">{{ $t('main.agent.label4') }}</div>
      </div>
      <!-- <div class="block" :class="{ active: navActive === 3 }" @click="changeNav(3)">
        <div class="label">下级</div>
      </div> -->
    </div>



    <div class="list">
      <van-swipe class="my-swipe" :loop="false" :show-indicators="false" ref="swipe" @change="e => {this.navActive = e}">
        <van-swipe-item>
          <div class="row">
            <div class="label">{{ $t('main.agent.label5') }}</div>
            <div class="value">{{ (agentTeamInfo.agent_total + agentTeamInfo.user_total) || 0 }}{{ $t('main.dealRecord.label11') }}</div>
          </div>
          <div class="row">
            <div class="label">{{ $t('main.agent.label6') }}</div>
            <div class="value">{{ agentTeamInfo.agent_total }}{{ $t('main.dealRecord.label11') }}</div>
          </div>
          <div class="row">
            <div class="label">{{ $t('main.agent.label7') }}</div>
            <div class="value">{{ agentTeamInfo.user_total }}{{ $t('main.dealRecord.label11') }}</div>
          </div>
          <div class="row">
            <div class="label">{{ $t('main.agent.label8') }}</div>
            <div class="value">{{ agentTeamInfo.balance_total }}</div>
          </div>
        </van-swipe-item>
        <van-swipe-item>
          <div class="row" style="border: 0">
            <div class="label">{{ $t('main.agent.label3') }}</div>
            <div class="input_wrap">
              <van-field ref="input" v-model="params.code" type="text" :placeholder="$t('main.dealRecord.label9')" autocomplete="off" :disabled="!showCode"/>
            </div>
          </div>
          <div class="button" @click="submit" v-if="showCode">{{ $t('main.dealRecord.label6') }}</div>
        </van-swipe-item>
        <van-swipe-item>
          <div class="row">
            <div class="label">{{ $t('main.agent.label10') }}</div>
            <div class="value">{{ fundsInfo.recharge }}MYR</div>
          </div>
          <div class="row">
            <div class="label">{{ $t('main.agent.label11') }}</div>
            <div class="value">{{ fundsInfo.withdraw }}MYR</div>
          </div>
        </van-swipe-item>
        <van-swipe-item>
          <AgentTree :data="childrenTree" @changeChildren="changeChildren"/>
        </van-swipe-item>
      </van-swipe>
    </div>

  </main>
</template>
<script>
import AgentTree from './components/agentTree.vue'
import { construct, destruct } from '@aximario/json-tree'
export default {
  components: { AgentTree },
  name: 'message',
  data() {
    return {
      params: {
        code: '',
      },
      agentTeamInfo: {},
      fundsInfo: {},
      navActive: 0,
      showCode: true,
      userInfo: {},
      childrenTree: [],
      childrenList: []
    }
  },
  created() {
    this.getAgentTeamInfo()
    this.getAgentTeamFunds()
    this.params.code = this.$store.state.userInfo.invite_code
    if(this.$store.state.userInfo.invite_code) {
      this.showCode = false
    }
    this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
    this.getChildren(this.userInfo.id)
  },
  methods: {
    async getAgentTeamInfo() {
      const res = await this.$apiFun.getAgentTeamInfoApi()
      this.agentTeamInfo = res.data
    },
    async getAgentTeamFunds() {
      const res = await this.$apiFun.getAgentTeamFundsApi()
      this.fundsInfo = res.data
    },
    async submit() {
      const res = await this.$apiFun.agentSaveApi(this.params)
      if(res.code === 200) {
        this.$parent.showTost('1', res.message)
        this.showCode = false
      }
    },
    async getChildren(pid) {
      const res = await this.$apiFun.agentChildrenApi({pid})
      this.childrenTree = construct(res.data)
      this.childrenList = res.data
    },

    changeChildren(data) {
      let list = this.childrenList
      let newList = []
      if(data.data.length === 0) {
        for(let i in list) {
          if(list[i].id == data.pid) {
            list[i].noChild = true
            break
          }
        }
        newList = list
      }else {
        newList = [...list, ...data.data]
      }
      console.log(newList)
      console.log(destruct(newList))
      this.$set(this, 'childrenList', destruct(newList))
      this.$set(this, 'childrenTree', construct(newList))
    },

    changeNav(e) {
      this.navActive = e
      this.$refs.swipe.swipeTo(e)
    }
  },
};
</script>
<style lang="scss" scoped>
header {
  height: 46px;
  .van-nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .right {
    position: fixed;
    display: flex;
    align-items: center;
    z-index: 1000;
    height: 46px;
    line-height: 46px;
    right: 20px;
    font-weight: 500;
    font-size: .35rem;
  }
}



.nav {
  height: 45px;
  display: flex;
  margin: 0 15px;
  .block {
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-right: 20px;
    .label {
      color: #606060;
      width: 100%;
      text-align: center;
      font-size: .35rem;
    }
  }
  .active {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 4px;
      border-radius: 4px;
      background: #CF866B;
      bottom: 2px;
    }
    .label {
      color: #3f4042;
      font-weight: 500;
    }
  }
}

.list {
  height: calc(var(--vh) * 100 - 91px);
  .row {
    display: flex;
    justify-content: space-between;
    height: 30px;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid #ddd;
  }
  .input_wrap {
    border: 1px solid #999;
    border-radius: 5px;
    overflow: hidden;
  }
  .button {
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #EA4236;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 10px;
  }

  .van-swipe__track, .my-swipe {
    height: 100%;
    margin: 0 10px;
  }
}
.van-cell {
  padding:  5px  8px !important;
}
</style>
