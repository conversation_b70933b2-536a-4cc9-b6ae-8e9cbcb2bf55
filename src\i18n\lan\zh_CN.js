export const foot = {
  index: '首页',
  activity: '优惠',
  kefu: '客服',
  zanzhu: '赞助',
  mine: '我的',
  loading: '加载中',
}


export const mode = {
  activity: {
    title: '优惠活动',
    total: '全部',
  },

  app: {
    title: 'app下载',
    label1: '可扫描下发二维码进行app下载',
    label2: '立即下载',
    label3: '需在同一网络环境下下载安装注册，请勿切换网络； 若无法正常安装，请使用手机自带浏览器打开本页面',
  },

  concise: {
    title1: 'OB电子',
    title2: 'FG电子',
    title3: 'PP电子',
    title4: 'AE电子',
  },

  hongbao: {
    label1: '剩余领取次数',
    label2: '次',
    label3: '已领取',
    label4: '当前最多可领取',
    label5: '快去满足条件吧！',
    label6: '活动时间',
    label7: '累计充值金额',
    label8: '红包次数',
    label9: '领取规则：',
    label10: '1.抢到红包后，系统自动派彩',
    label11: '秒到账',
    label12: '达到流水倍数即可取款；',
    label13: '2.领取红包条件：充值金额需要在规定的活动日期（美东时间）范围内，根据充值累计金额，获取抢红包次数，即可抢对应次数的红包；',
    label14: '金沙集团官网',
    label15: '注意：',
    label16: '抢红包',
    label17: '北京时间为每天早上10点到12点，存款计算为前一天12点到今天10点，谢谢',
    label18: '1、会员必须在指定的活动日期（美东时间）范围内，根据充值累计金额，即可获得对应抢红包次数。若在规定的时间范围内没有达到存款金额范围，则不计算抢红包次数，逾期作废！',
    label19: '2、所有的活动优惠特为玩家而设，如发现任何团体或个人，以不诚实的方式套取红利或任何威胁、滥用公司优惠等行为，公司保留冻结、取消该团体或个人账户及账户结余的权利。',
    label20: '3、金沙集团官网保留所有解释权，在任何时候都可以更改、停止、取消优惠活动。',
    label21: '每日百万现金红包！存款越多，机会越多，红包享不停，惊喜抢不停，还等什么？快快叫上好友一起分享吧！！',
    label22: '当前时间段',
    label23: '活动还没开始，请静待活动开始。',
    label24: '请静待下次活动。',
    label25: '恭喜您',
    label26: '抢到',
    label27: '元',
    label28: '立即充值',
    toast1: '您暂未达到领取条件，快去完成吧！',
    toast2: '服务器异常，请稍后再试',
  },

  index: {
    label1: '真人娱乐，体育投注，电子游艺等尽在一手掌握',
    label2: '客服',
    label3: '更多公告',
    label4: '未登录',
    label5: '登录后查看',
    label6: '存款',
    label7: '转账',
    label8: '取款',
    label9: '真人',
    label10: '体育',
    label11: '电竞',
    label12: '棋牌',
    label13: '电子',
    label14: '彩票',
    label15: 'Hi，欢迎进入',
    label16: '专属VIP体验',
    label17: '立享会员特权',
    label18: '享受只属于你的与众不同',
    label19: '会员中心',
    label20: '消息中心',
    label21: '意见反馈',
    label22: '永久域名',
    label23: '立即登录',
    label24: '安全退出',
    label25: '欢迎来到',
    label26: '捕鱼',
    label27: '更多公告',
    label28: '',
    toast1: '复制成功！',
    windowTitle: '温馨提示',
    windowLabel1: '新濠国际平台目前已开放TNG和USDT充值和取款通道。',
    windowLabel2: '谢谢大家一直对我们平台的支持。',
  },

  mine: {
    title: '我的',
    kefu: '客服',
    label1: '中心钱包',
    label2: 'VIP特权',
    label3: '豪礼赠送',
    label4: '我的钱包',
    label5: '交易记录',
    label6: '投注记录',
    label7: '账户设置',
    label8: '消息中心',
    label9: '返水中心',
    label10: '活动记录',
    label11: '福利中心',
    label12: '合营计划',
    label13: '加入我们，共赢财富',
    label14: '代理登录',
    label15: 'app下载',
    label16: '帮助中心',
    label17: '退出系统',
    label18: '加入',
    label19: '第',
    label20: '天',
    label21: '语言',
    label22: 'USDT钱包',
    label23: '佣金钱包',
  },

  transfer: {
    title: '转账',
    label1: '一键回收',
    label2: '登录',
    label3: '注册',
    label4: '游戏平台',
    label5: '免转模式',
    label6: '额度转换',
    label7: '转入',
    label8: '转出',
    label9: '提交',
    label10: '额度转换',
    label11: '平台账户余额',
    label12: '游戏账户余额',
    label13: '操作金额',
    label14: '操作金额',
    label15: '填写操作金额',
    label16: '转账方式',
    label17: '平台钱包',
    toast1: '请输入操作金额！',
    toast2: '操作成功！',
  },

  zanzhu: {
    title: '赞助',
    label1: '尤文图斯',
    label2: '官方区域合作伙伴',
    label3: '阿斯顿维拉',
    label4: '官方全球顶级合作伙伴',
    label5: '没有更多了',
    label6: '',
    label7: '',
    label8: '',
    label9: '',
    label10: '',
    label11: '',
    label12: '',
    label13: '',

  },
}


export const main = {
  activityInfo: {
    title: '活动详情',
    label1: '活动说明',
    label2: '立即申请',
    label3: '前往登录',
  },

  activityRecord: {
    title: '活动申请记录',
    label1: '没有更多了',
    label2: '活动标题：',
    label3: '申请时间：',
    label4: '状态：',
    label5: '空空如也',
    label6: '未约定',
    label7: '待审核',
    label8: '通过',
    label9: '拒绝',
    label10: '未约定',
  },

  addBankCard: {
    title: '新增银行卡',
    label1: '持卡人姓名',
    label2: '为了您的资金能够迅速到账，请确保填写的姓名与银行卡的开户姓名一致',
    label3: '银行类型',
    label4: '银行账号',
    label5: '开户行',
    label6: '支付密码',
    label7: '确认添加',
    label8: '银行类型',
    placeholder1: '请输入持卡人姓名',
    placeholder2: '请选择银行类型',
    placeholder3: '请输入银行账号',
    placeholder4: '请输入开户行',
    placeholder5: '请输入支付密码',
    toast1: '请输入姓名',
    toast2: '请输入银行',
    toast3: '请输入开户行地址',
    toast4: '请输人银行账号',
    toast5: '请输人支付密码',
    toast6: '请输人正确的卡号长度',
    toast7: '请输人支付密码长度',
    toast8: '绑定成功',
  },

  addUsdtCard: {
    title: '新增USDT地址',
    label1: 'USDT价格稳定 流通性高 不受监管',
    label2: '了解更多',
    label3: '绑定协议地址',
    label4: '交易所划转',
    label5: '完成取款',
    label6: '钱包协议',
    label7: 'USDT地址',
    label8: '支付密码',
    label9: '确认添加',
    placeholder1: '请输入USDT地址',
    placeholder2: '请输入支付密码',
    toast1: '请输入USDT地址',
    toast2: '请选择钱包协议',
    toast3: '请输人支付密码',
  },

  applyagent: {
    title: '合营计划',
    label1: '合营部',
    label2: '立即咨询',
    label3: '咨询',
    label4: '加入我们',
    label5: '用户名',
    label6: '真实姓名',
    label7: '联系方式',
    label8: '申请理由',
    placeholder1: '请输入您的联系方式',
    placeholder2: '请输入申请说明',
    toast1: '请输入正确手机号',
    toast2: '请输入申请理由',
  },

  betRecord: {
    title: '投注记录',
    label1: '订单号：',
    label2: '金额',
    label3: '派彩',
    label4: '空空如也',
    label5: '今日',
    label6: '近7日',
    label7: '近15日',
    label8: '近30日',
    label9: '没有更多了',
    label10: '无效注单',
    label11: '已结算',
    label12: '未结算',
    label13: '全平台',
  },

  boutBallBet: {
    label1: '尊敬的会员用户',
    label2: '早上好，欢迎来到帮助中心',
    label3: '若相关问题仍未解决，可咨询在线客服',
    label4: '关于我们',
    label5: '常见问题',
    label6: '隐私政策',
    label7: '免责说明',
    label8: '联系我们',
    label9: '代理加盟',
    label10: '博彩责任',
    label11: '没有找到解决办法？请联系',
    label12: '人工客服',
    label13: '解决',
  },

  boutBallBetInfo: {
    label4: '常见问题',
    label5: '隐私政策',
    label6: '免责说明',
    label7: '联系我们',
    label8: '代理加盟',
    label9: '关于我们',
    label10: '博彩责任',
    label11: '没有找到解决办法？请联系',
  },

  fanshui: {
    title: '返水中心',
    label1: '返水记录',
    label2: '点击领取',
    label3: '累计领取',
    label4: '待领取',
    label5: '返水金额',
    label6: '返水时间',
    label7: '领取时间：',
    label8: '空空如也',
    label9: '没有更多了',
    label10: '暂未领取',
    toast1: '暂无领取额度！',
    label12: '',
  },

  login: {
    title: '登录',
    label1: '先去逛逛',
    label2: '在线客服',
    label3: '注册新用户',
    label4: '返回登录',
    label5: '注册',
    label6: '记住账号/密码',
    placeholder1: '用户名',
    placeholder2: '密码',
    placeholder3: '验证码',
    placeholder4: '用户名',
    placeholder5: '登录密码',
    placeholder6: '确认密码',
    placeholder7: '真实姓名',
    placeholder8: '支付密码',
    placeholder9: '验证码',
    placeholder10: '邀请码',
    toast1: '用户名长度6~16位，以字母或数字组合！',
    toast2: '请输入正确的密码长度，最少6位！',
    toast3: '两次密码不一致！',
    toast4: '请输入您的真实姓名!',
    toast5: '请输入正确的支付密码长度，最少6位！',
    toast6: '请输入验证码！',
    toast7: '验证码错误！',
    toast8: '请输入您的账号和密码！',
    toast9: '请输入邀请码！',
  },

  message: {
    title: '消息中心',
    label1: '公告',
    label2: '站内信',
    label3: '没有更多了',
  },

  money: {
    title: '我的钱包',
    label1: '总资产（元）',
    label2: '中心钱包（元）',
    label3: '中心钱包',
    label4: '游戏钱包',
    label5: '存款',
    label6: '转账',
    label7: '取款',
    label8: '卡片管理',
    label9: '场馆余额',
    label10: '一键回收',
    label11: '刷新',
  },

  password: {
    title1: '修改登录密码',
    title2: '设置提现密码',
    label1: '原密码',
    label2: '新密码',
    label3: '确认新密码',
    label4: '确认修改',
    placeholder1: '请输入当前密码',
    placeholder2: '请输入新密码',
    placeholder3: '请再次输入密码',
    toast1: '请输入旧密码',
    toast2: '请输入新密码',
    toast3: '请输入正确的旧密码长度',
    toast4: '请输入正确的新密码长度',
    toast5: '请输入确认密码',
    toast6: '两次密码不一致！',
    toast7: '新旧密码不能一致！',
    toast8: '密码修改成功！',
  },

  payInfo: {
    label1: '充值信息',
    label2: '元',
    label3: '请在',
    label4: '内完成支付',
    label5: '成功付款后，将自动到账！',
    label6: '如有问题，请',
    label7: '联系客服',
    label8: '确认',
    label9: '收款地址',
    label10: '复制',
    label11: '订单号',
    label12: '交易时间',
    label13: '充值方式',
    label14: '钱包协议',
    label15: '充值完成',
    label16: '资金明细',
    label17: '复制成功！',
  },

  recharge: {
    title: '存款',
    label1: '网银转账',
    label2: '微信',
    label3: '支付宝',
    label4: '收款账号',
    label5: '复制',
    label6: '银行户名',
    label7: '开户行',
    label8: '银行地址',
    label9: '开户银行',
    label10: '存款人姓名',
    label11: '为及时到账，请务必输入正确的存款人姓名',
    label12: '银行账号',
    label13: '开户行地址',
    label14: '存款金额',
    label15: '元',
    label16: 'USDT价格稳定 流通性高 不受监管',
    label17: '了解更多',
    label18: '绑定协议地址',
    label19: '交易所划转',
    label20: '完成取款',
    label21: '钱包协议',
    label22: '存款金额',
    label23: '参考汇率',
    label24: '温馨提示',
    label25: '请选择正确的USDT协议付款，若您选择错误的协议付款，平台将无法收到您的付款，为此我们不承担任何负责！',
    label26: '存款金额',
    label27: '立即存款',
    label28: '存款遇到问题？联系',
    label29: '人工客服',
    label30: '解决',
    label31: '温馨提示',
    label32: '您还为绑定任何钱包卡片，请前往绑定！',
    label33: '前往绑定',
    label34: '请输入金额在',
    label35: '之间！',
    label36: '收款账号',
    label37: '完成向以下账户的直接银行转账后，请填写以下信息。',
    label38: '存款凭证照片',
    label39: '温馨提示：',
    label40: '充值最低RM500起',
    label41: '充值最低100USDT起',
    placeholder1: '选择开户银行',
    placeholder2: '请输入存款人姓名',
    placeholder3: '请输入银行账号',
    placeholder4: '请输入开户行地址',
    placeholder5: '请输入取款金额',
    placeholder6: '请输入USDT地址',
    placeholder7: '请输入取款金额',
    placeholder8: '请输入取款金额',
    placeholder9: '请上传存款凭证照片',
    toast1: '请输入存款人姓名',
    toast2: '请输入银行类型',
    toast3: '请输入银行账号',
    toast4: '请输入银行开户行地址',
    toast5: '提交成功，等待后台审核',
    toast6: '复制成功！',
    other1: '钱包地址',
    other2: '备注',
    other3: '请输入备注',
  },

  register: {
    toast1: '请阅读并同意相关条款和隐私协议！',
    toast2: ' 用户名长度6~16位，以字母或数字组合！',
    toast3: '请输入正确的密码长度，最少6位！',
    toast4: '两次密码不一致！',
    toast5: '请输入您的真实姓名!',
    toast6: '请输入正确的支付密码长度，最少6位！',
  },


  transfer: {
    title: '转账',
    label1: '钱包金额',
    label2: '一键回收',
    label3: '中心钱包',
    label4: '游戏钱包',
    label5: '收起',
    label6: '展开',
    label7: '自动免转',
    label8: '开启后余额自动转入游戏场馆',
    label9: '场馆内钱包不支持互转',
    label10: '最大金额',
    label11: '立即转账',
    label12: '转账遇到问题？联系',
    label13: '人工客服',
    label14: '解决',
    label15: '选择钱包',
    label16: '平台钱包',
    toast1: '请输入操作金额！',
    toast2: '场馆内钱包不支持互转',
    toast3: '请输入操作金额！',
    toast4: '操作成功！',
    placeholder1: '请输入转账金额',
  },

  transRecord: {
    title: '交易记录',
    label1: '没有更多了',
    label2: '订单号：',
    label3: '金额',
    label4: '空空如也',
    label5: '今日',
    label6: '近7日',
    label7: '近15日',
    label8: '全部',
    label9: '存款',
    label10: '取款',
    label11: '转入',
    label12: '转出',
    label13: '全平台',
    label14: '未定义',
    label15: '待审核',
    label16: '审核通过',
    label17: '审核拒绝',
    label18: '失败',
    label19: '成功',
    label20: '待结算',
    label21: '未定义',
    label22: '撤销',
    label23: '已取消',
    label24: '充值记录',
    label25: '提款记录'
  },

  usdtmore: {
    title: '协议的区别',
    label1: '协议介绍',
    label2: 'TRC20：基于波场网络协议',
    label3: 'TRC20的提币手续费最低，这意味着用户可以享受低手续费的交易所提币服务。同时，波场网络的TPS能够达到上千级别，可以实现交易秒级确认。',
    label4: 'ERC20：基于以太坊网络协议',
    label5: '以太坊发行自己的原生代币及其他代币。但成千上万种代币的规则都不一样，这对后期市场发展非常不利。所以代币发行者做了一个智能合约标准，也就是ERC20。',
    label6: '协议的区别？',
    label7: '区别点',
    label8: 'TRC20协议',
    label9: 'ERC20协议',
    label10: '地址样式',
    label11: 'USDT地址以T开头',
    label12: 'USDT地址以0x开头',
    label13: '使用网络',
    label14: '波场网络',
    label15: '以太坊网络',
    label16: '网络状态',
    label17: '基本不堵',
    label18: '经常拥堵',
    label19: '转账速度',
    label20: '极快',
    label21: '几秒到几分钟',
    label22: '几分钟到数十分钟',
    label23: '手续费用',
    label24: '低',
    label25: '普通',
    label26: '安全系数',
    label27: '普通',
    label28: '高',
    label29: '使用建议',
    label30: '小额高频',
    label31: '交易推荐',
    label32: '中等额度',
    label33: '常规交易推荐',
    label34: '到底选哪种协议更好？',
    label35: '小额交易推荐',
    label36: '低手续费，秒级到账。',
    label37: '中等额度推荐',
    label38: '手续费和速度都介于中间值。',
    label39: '两种协议对应的USDT地址不互通，进行转账、充值等操作时，应仔细核对正确地址！',
    label40: '普通',
  },

  vip: {
    title: 'VIP特权',
    label1: '累计存款',
    label2: '流水要求',
    label3: 'VIP最高返水比例',
    label4: 'VIP等级',
    label5: '真人',
    label6: '体育',
    label7: '电竞',
    label8: '棋牌',
    label9: '电子',
    label10: '彩票',
    label11: '活动规则',
    label12: '晋升标准',
    label13: '会员的累计存款以及累计流水达到相应级别的要求，即可在次日24点前晋级相应VIP等级。',
    label14: '晋升顺序',
    label15: 'VIP等级达到相应的要求可每天晋升一级，但VIP等级不可越级晋升。',
    label16: '保级要求',
    label17: '会员在达到某VIP等级后，90天内投注需要完成保级流水要求。如果在此期间完成晋升，保级要求重新按照当前等级计算。',
    label18: '降级标准',
    label19: '如果会员在一个季度（90天计算）内没有完成相应的保级要求流水，系统会自动降级一个等级，相应的返水及其它优惠也会随之调整至降级后的等级。',
    label20: 'VIP返水',
    label21: 'VIP返水优惠发放金额根据会员当天北京时间00:00—23:59之间的有效投注进行计算，当天所有的投注额返水将在注单结算当天结束后24小时内发放到福利中心，进入个人中心点击福利中心进行手动领取。（VIP返水1倍流水即可提款）',
    label22: '欧宝娱乐保留对活动的修改，停止及最终解释权',
    label23: '已达标',
    label24: '未达标',
  },

  userCent: {
    title: '个人资料',
    label1: '完善账户信息，更安全',
    label2: '完善个人资料',
    label3: '资料更完整，我们的服务更加周到',
    label4: '去完善',
    label5: '卡片管理',
    label6: '如需提现，请绑定银行卡或虚拟币地址',
    label7: '登录密码管理',
    label8: '定期修改登录密码，有利账户安全',
    label9: '取款密码管理',
    label10: '定期修改登录密码，有利账户安全',
  },

  userInfo: {
    title: '个人资料',
    label1: '个人头像',
    label2: '用户名',
    label3: '真实姓名',
    label4: '出生日期',
    label5: '手机号码',
    label6: '电子邮箱',
    label7: '确认修改',
    placeholder1: '请输入用户名',
    placeholder2: '请输入真实姓名',
    placeholder3: '请选择出入日期',
    placeholder4: '绑定手机号，保障账号安全',
    placeholder5: '绑定邮箱保护账号安全',
    toast1: '请输入正确手机号',
    toast2: '请输入正确邮箱号',
    toast3: '请输入正确的日期格式：YYYY-MM-DD',
    toast4: '操作成功',
  },

  wallet: {
    title: '卡片管理',
    label1: '虚拟币',
    label2: '银行卡',
    label3: '添加USDT地址',
    label4: '最多支持添加5个地址',
    label5: '添加银行卡',
    label6: '最多支持添加5张银行卡',
    toast1: '温馨提示',
    toast2: '确定要解除绑定该卡片吗？',
    toast3: '解绑成功',
  },

  welfare: {
    title: '福利中心',
    label1: '红包记录',
    label2: '前往领取',
    label3: '剩余领取次数：',
    label4: '已领取次数：',
    label5: '充值金额',
    label6: '红包金额：',
    label7: '充值时间',
    label8: '领取时间：',
    label9: '空空如也',
    label10: '没有更多了',
  },

  withdrawal: {
    title: '取款',
    label1: 'USDT取款',
    label2: '银行卡取款',
    label3: '钱包金额',
    label4: '一键回收',
    label5: '中心钱包',
    label6: '游戏钱包',
    label7: '收起',
    label8: '展开',
    label9: '选择USDT地址',
    label10: '添加USDT地址',
    label11: '打码量',
    label12: '取款金额',
    label13: '最大金额',
    label14: '支付密码',
    label15: '每笔手续费',
    label16: '折合USDT',
    label17: '参考汇率：',
    label18: '实时变化',
    label19: '实际到账：',
    label20: '选择银行卡',
    label21: '添加银行卡',
    label22: '请选择银行卡',
    label23: '请选择USDT地址',
    label24: '立即取款',
    label25: '取款遇到问题？联系',
    label26: '人工客服',
    label27: '解决',
    label28: '佣金提款',
    label29: '温馨提示：',
    label30: '取款最低RM50起',
    placeholder1: '打码量',
    placeholder2: '请输入取款金额',
    placeholder3: '请输入支付密码',
    toast1: '请选择您要提现到的银行卡',
    toast2: '单笔取款不能低于50元',
    toast3: '请输入您的支付密码',
    toast4: '提交成功，等待后台审核',
    toast5: '请选择USDT地址',
    toast6: '单笔取款不能低于10usdt',
    toast7: '请输入您的支付密码',
    toast8: '提交成功，等待后台审核',
    errorTitle: '温馨提示',
    errorContent: '平台为配合马来西亚政府反洗黑钱，加密货币通道(USDT)暂时关闭，开放时间另行通知。'
  },

  lottery: {
    label1: '余额',
    label2: '期',
    label3: '期截止',
    label4: '清空',
    label5: '马上投注',
    label6: '目前号码',
    label7: '每注金额',
    label8: '最高可中',
    label9: '共',
    label10: '期数',
    label11: '奖号',
    label12: '和值',
    label13: '距',
    label14: '赔率',
    label15: '注',
    label16: '没有更多了',
    toast1: '请选择投注点数',
    toast2: '请输入投注金额',
    toast3: '期投注时间已截止',
    pk10Type1: '1-10名',
    pk10Type2: '冠亚和',
  },
  
  dealRecord: {
    label1: '账变记录',
    label111: '佣金账变记录',
    label2: '我的',
    label3: '下级',
    label4: '金额',
    label5: '可用余额',
    label6: '确定',
    label7: '取消',
    label8: '请选择起始时间',
    label9: '请选择结束时间',
    label10: '全部',
    label11: '人',
    toast1: '起始时间和结束时间需要在同一个月',
    toast2: '起始时间不能大于结束时间',
  },

  coinTrans: {
    label1: '货币兑换',
    label2: 'MYR 转 USDT',
    label3: 'USDT 转 MYR',
    label4: '请输入兑换金额',
    label5: '请输入资金密码',
  },

  agent: {
    label1: '代理中心',
    label2: '概况',
    label3: '邀请码',
    label4: '报表',
    label5: '团队人数',
    label6: '代理人数',
    label7: '玩家人数',
    label8: '团队余额',
    label9: '请输入邀请码',
    label10: '储值',
    label11: '提款',
  },

  trans: {
    label1: '划账',
    label2: '请输入下级账号',
    label3: '请输入划账金额',
    label4: '请输入资金密码',
  },

  betList: {
    label1: '投注列表',
    label2: '期数',
    label3: '投注内容',
    label4: '购买成功',
    label5: '已中奖',
    label6: '未中奖',
    label7: '未开奖',
    label16: '投注单号',
    label17: '游戏名称',
    label18: '游戏玩法',
    label19: '投注时间',
    label20: '游戏账号',
    label21: '投注期号',
    label22: '投注号码',
    label23: '投注金额',
    label24: '开奖号码',
    label25: '订单状态',
    label28: '开奖详情',
    label29: '已中奖',
    label30: '未中奖',
    label31: '未开奖',
    label32: '中奖金额',
    label33: '期',
  },
}


export const other = {
  label1: '提示',
  label2: '您确定要退出登录吗?',
  label3: '请登录！',
  label4: '复制成功！',
  label5: '精彩内容等你来体验，快来登录吧！',
  label6: '红包已关闭',
  label7: '已在当前页面！',
  label8: '认证失败',
  label9: '您的账号登陆过期，请重新登陆',
}

export const yingkui = {
  label1: '盈亏',
  label2: '今日',
  label3: '昨天',
  label4: '前天',
  label5: '近七日',
  label6: '近15日',
  label7: '收益',
  label8: '投注金额',
  label9: '中奖金额',
  label10: '昨日收益',
  label11: '请选择月份'
}

export const kefu = {
  tip1: '点击下面您想要的通讯软件的客户中心即可立即连接。',
  tip2: '如果您有任何疑问，可以通过Telegram或WhatsApp获得1对1咨询服务。点击聊天ID即可自动连接客服人员获取帮助。',
  tip3: '* 休息期间回复可能会延迟。',
  error: '很抱歉，此软件暂不支持该地区服务。'
}