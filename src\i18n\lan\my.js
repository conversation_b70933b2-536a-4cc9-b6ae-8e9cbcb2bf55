export const foot = {
  index: '<PERSON><PERSON>',
  activity: '<PERSON>mosi',
  kefu: '<PERSON><PERSON><PERSON><PERSON>',
  zanzhu: '<PERSON><PERSON>',
  mine: '<PERSON><PERSON>',
  loading: 'Me<PERSON>at<PERSON>',
}


export const mode = {
  activity: {
    title: 'Aktiviti Promosi',
    total: 'Semu<PERSON>',
  },

  app: {
    title: 'Muat turun aplikasi',
    label1: 'Anda boleh mengimbas kod QR di bawah untuk memuat turun aplikasi',
    label2: 'Muat turun sekarang',
    label3: 'Sila muat turun, pasang dan daftar dalam persekitaran rangkaian yang sama, jangan tukar rangkaian; jika tidak dapat dipasang dengan normal, sila gunakan pelayar telefon bimbit untuk membuka halaman ini',
  },

  concise: {
    title1: 'OB Elektronik',
    title2: 'FG Elektronik',
    title3: 'PP Elektronik',
    title4: 'AE Elektronik',
  },

  hongbao: {
    label1: '<PERSON><PERSON> kali penerimaan',
    label2: 'kali',
    label3: 'Telah diterima',
    label4: 'Maksimum boleh diterima sekarang',
    label5: 'Sila pergi memenuhi syarat!',
    label6: 'Tempoh aktiviti',
    label7: 'Jumlah deposit terkumpul',
    label8: 'Bilangan sampul merah',
    label9: 'Peraturan penerimaan:',
    label10: '1. Selepas mendapat sampul merah, sistem akan membayar secara automatik',
    label11: 'kredit segera',
    label12: 'Boleh mengeluarkan wang setelah mencapai gandaan aliran yang diperlukan;',
    label13: '2. Syarat untuk menerima sampul merah: jumlah deposit perlu berada dalam lingkungan tarikh aktiviti yang ditetapkan (Waktu Timur), berdasarkan jumlah deposit terkumpul, dapatkan bilangan kali untuk merebut sampul merah, boleh merebut sampul merah sebanyak bilangan kali yang sesuai;',
    label14: 'Laman rasmi Kumpulan Sands',
    label15: 'Perhatian:',
    label16: 'Rebut sampul merah',
    label17: 'Setiap hari dari 9 pagi hingga 9 malam, pengiraan deposit adalah dari jam 9 pagi hari sebelumnya hingga jam 9 pagi hari ini, terima kasih',
    label18: '1. Ahli mesti berada dalam lingkungan tarikh aktiviti yang ditetapkan (Waktu Timur), berdasarkan jumlah deposit terkumpul, untuk mendapatkan bilangan kali merebut sampul merah yang sesuai. Jika tidak mencapai jumlah deposit yang ditetapkan dalam tempoh yang ditetapkan, bilangan kali merebut sampul merah tidak akan dikira, dan akan luput!',
    label19: '2. Semua promosi adalah untuk pemain, jika terdapat mana-mana kumpulan atau individu yang didapati menggunakan cara yang tidak jujur untuk mendapatkan bonus atau sebarang ancaman, penyalahgunaan promosi syarikat dan sebagainya, syarikat berhak untuk membekukan, membatalkan akaun dan baki akaun kumpulan atau individu tersebut.',
    label20: '3. Laman rasmi Kumpulan Sands berhak untuk mengubah, menghentikan, atau membatalkan aktiviti promosi pada bila-bila masa.',
    label21: 'Sampul merah wang tunai berjuta setiap hari! Lebih banyak deposit, lebih banyak peluang, nikmati sampul merah tanpa henti, kejutan tanpa henti, apa lagi yang anda tunggu? Segera ajak rakan-rakan anda untuk berkongsi bersama!!',
    label22: 'Tempoh masa semasa',
    label23: 'Aktiviti belum bermula, sila tunggu aktiviti bermula.',
    label24: 'Sila tunggu aktiviti seterusnya.',
    label25: 'Tahniah',
    label26: 'Anda telah merebut',
    label27: 'RM',
    label28: 'Deposit sekarang',
    toast1: 'Anda belum memenuhi syarat penerimaan, sila pergi dan selesaikannya!',
    toast2: 'Ralat pelayan, sila cuba lagi kemudian',
  },

  index: {
    label1: 'Hiburan langsung, pertaruhan sukan, permainan elektronik dan banyak lagi dalam genggaman anda',
    label2: 'Khidmat Pelanggan',
    label3: 'Lebih banyak pengumuman',
    label4: 'Belum log masuk',
    label5: 'Log masuk untuk melihat',
    label6: 'Top-up',
    label7: 'Pindahan',
    label8: 'Pengeluaran',
    label9: 'Permainan Langsung',
    label10: 'Sukan',
    label11: 'E-Sukan',
    label12: 'Permainan Kad',
    label13: 'Permainan Elektronik',
    label14: 'Loteri',
    label15: 'Selamat datang ke ',
    label16: 'Pengalaman VIP eksklusif',
    label17: 'Nikmati keistimewaan ahli',
    label18: 'Nikmati pengalaman unik hanya untuk anda',
    label19: 'Pusat Ahli',
    label20: 'Pusat Mesej',
    label21: 'Maklum Balas',
    label22: 'Domain Kekal',
    label23: 'Log Masuk Sekarang',
    label24: 'Log Keluar',
    label25: 'Selamat datang ke',
    label26: 'Memancing',
    label27: 'Lebih banyak pengumuman',
    label28: '',
    toast1: 'Berjaya disalin!',
    windowTitle: 'Perhatian',
    windowLabel1: 'Saluran deposit dan pengeluaran melalui TNG dan USDT kini telah dibuka di platform Melco International.',
    windowLabel2: 'Terima kasih atas sokongan anda terhadap platform kami.'
  },

  mine: {
    title: 'Saya',
    kefu: 'Khidmat Pelanggan',
    label1: 'Dompet Pusat',
    label2: 'Keistimewaan VIP',
    label3: 'Hadiah',
    label4: 'Dompet Saya',
    label5: 'Rekod Transaksi',
    label6: 'Rekod Pertaruhan',
    label7: 'Tetapan Akaun',
    label8: 'Pusat Mesej',
    label9: 'Pusat Rebat',
    label10: 'Rekod Aktiviti',
    label11: 'Pusat Kebajikan',
    label12: 'Rancangan Perkongsian',
    label13: 'Sertai kami, kongsi kekayaan',
    label14: 'Log Masuk Ejen',
    label15: 'Muat turun aplikasi',
    label16: 'Pusat Bantuan',
    label17: 'Log Keluar',
    label18: 'Sertai',
    label19: 'Hari',
    label20: 'ke',
    label21: 'Bahasa',
    label22: 'Dompet USDT',
    label23: 'Dompet Komisen',
  },

  transfer: {
    title: 'Pindahan',
    label1: 'Pulihkan Semua',
    label2: 'Log Masuk',
    label3: 'Daftar',
    label4: 'Platform Permainan',
    label5: 'Mod Bebas Pindahan',
    label6: 'Penukaran Had',
    label7: 'Pindah Masuk',
    label8: 'Pindah Keluar',
    label9: 'Hantar',
    label10: 'Penukaran Had',
    label11: 'Baki Akaun Platform',
    label12: 'Baki Akaun Permainan',
    label13: 'Jumlah Operasi',
    label14: 'Jumlah Operasi',
    label15: 'Isi jumlah operasi',
    label16: 'Kaedah Pindahan',
    label17: 'Dompet Platform',
    toast1: 'Sila masukkan jumlah operasi!',
    toast2: 'Operasi berjaya!',
  },

  zanzhu: {
    title: 'Penaja',
    label1: 'Juventus',
    label2: 'Rakan Kerjasama Daerah Rasmi',
    label3: 'Aston Villa',
    label4: 'Rakan Kerjasama Global Peringkat Teratas Rasmi',
    label5: 'Tiada lagi',
    label6: '',
    label7: '',
    label8: '',
    label9: '',
    label10: '',
    label11: '',
    label12: '',
    label13: '',

  },
}


export const main = {
  activityInfo: {
    title: 'Butiran Aktiviti',
    label1: 'Penerangan Aktiviti',
    label2: 'Mohon Sekarang',
    label3: 'Pergi Log Masuk',
  },

  activityRecord: {
    title: 'Rekod Permohonan Aktiviti',
    label1: 'Tiada lagi',
    label2: 'Tajuk Aktiviti:',
    label3: 'Masa Permohonan:',
    label4: 'Status:',
    label5: 'Kosong',
    label6: 'Tidak Dijadualkan',
    label7: 'Menunggu Semakan',
    label8: 'Lulus',
    label9: 'Tolak',
    label10: 'Tidak Dijadualkan',
  },

  addBankCard: {
    title: 'Tambah Kad Bank Baharu',
    label1: 'Nama Pemegang Kad',
    label2: 'Untuk memastikan dana anda sampai dengan cepat, pastikan nama yang diisi sama dengan nama di kad bank anda',
    label3: 'Bank',
    label4: 'Account no',
    label5: 'Cawangan Bank',
    label6: 'Kata Laluan Pembayaran',
    label7: 'Sahkan Penambahan',
    label8: 'Bank',
    placeholder1: 'Sila masukkan nama pemegang kad',
    placeholder2: 'Sila pilih bank',
    placeholder3: 'Sila masukkan account no',
    placeholder4: 'Sila masukkan cawangan bank',
    placeholder5: 'Sila masukkan kata laluan pembayaran',
    toast1: 'Sila masukkan nama',
    toast2: 'Sila masukkan bank',
    toast3: 'Sila masukkan alamat cawangan bank',
    toast4: 'Sila masukkan account no',
    toast5: 'Sila masukkan kata laluan pembayaran',
    toast6: 'Sila masukkan panjang nombor kad yang betul',
    toast7: 'Sila masukkan panjang kata laluan pembayaran',
    toast8: 'Pendaftaran berjaya',
  },

  addUsdtCard: {
    title: 'Tambah Alamat USDT Baharu',
    label1: 'Harga USDT stabil, kecairan tinggi, tidak dikawal selia',
    label2: 'Ketahui lebih lanjut',
    label3: 'Ikat Alamat Protokol',
    label4: 'Pemindahan Pertukaran',
    label5: 'Selesai Pengeluaran',
    label6: 'Protokol Dompet',
    label7: 'Alamat USDT',
    label8: 'Kata Laluan Pembayaran',
    label9: 'Sahkan Penambahan',
    placeholder1: 'Sila masukkan alamat USDT',
    placeholder2: 'Sila masukkan kata laluan pembayaran',
    toast1: 'Sila masukkan alamat USDT',
    toast2: 'Sila pilih protokol dompet',
    toast3: 'Sila masukkan kata laluan pembayaran',
  },

  applyagent: {
    title: 'Rancangan Perkongsian',
    label1: 'Jabatan Perkongsian',
    label2: 'Tanya Sekarang',
    label3: 'Pertanyaan',
    label4: 'Sertai Kami',
    label5: 'Nama Pengguna',
    label6: 'Nama Sebenar',
    label7: 'Maklumat Hubungan',
    label8: 'Sebab Permohonan',
    placeholder1: 'Sila masukkan maklumat hubungan anda',
    placeholder2: 'Sila masukkan penerangan permohonan',
    toast1: 'Sila masukkan nombor telefon yang betul',
    toast2: 'Sila masukkan sebab permohonan',
  },

  betRecord: {
    title: 'Rekod Pertaruhan',
    label1: 'Nombor Pesanan:',
    label2: 'Jumlah',
    label3: 'Pembayaran',
    label4: 'Kosong',
    label5: 'Hari Ini',
    label6: '7 Hari Lepas',
    label7: '15 Hari Lepas',
    label8: '30 Hari Lepas',
    label9: 'Tiada lagi',
    label10: 'Pertaruhan Tidak Sah',
    label11: 'Telah Diselesaikan',
    label12: 'Belum Diselesaikan',
    label13: 'Seluruh Platform',
  },

  boutBallBet: {
    label1: 'Ahli yang dihormati',
    label2: 'Selamat pagi, selamat datang ke Pusat Bantuan',
    label3: 'Jika masalah berkaitan masih belum diselesaikan, anda boleh merujuk kepada khidmat pelanggan dalam talian',
    label4: 'Tentang Kami',
    label5: 'Soalan Lazim',
    label6: 'Dasar Privasi',
    label7: 'Penafian',
    label8: 'Hubungi Kami',
    label9: 'Keahlian Ejen',
    label10: 'Tanggungjawab Perjudian',
    label11: 'Tidak menemui penyelesaian? Sila hubungi',
    label12: 'Khidmat Pelanggan Manusia',
    label13: 'untuk penyelesaian',
  },

  boutBallBetInfo: {
    label4: 'Soalan Lazim',
    label5: 'Dasar Privasi',
    label6: 'Penafian',
    label7: 'Hubungi Kami',
    label8: 'Keahlian Ejen',
    label9: 'Tentang Kami',
    label10: 'Tanggungjawab Perjudian',
    label11: 'Tidak menemui penyelesaian? Sila hubungi',
  },

  fanshui: {
    title: 'Pusat Rebat',
    label1: 'Rekod Rebat',
    label2: 'Klik untuk Menerima',
    label3: 'Jumlah Penerimaan',
    label4: 'Menunggu Penerimaan',
    label5: 'Jumlah Rebat',
    label6: 'Masa Rebat',
    label7: 'Masa Penerimaan:',
    label8: 'Kosong',
    label9: 'Tiada lagi',
    label10: 'Belum Diterima',
    toast1: 'Tiada had penerimaan buat masa ini!',
    label12: '',
  },

  login: {
    title: 'Log Masuk',
    label1: 'Lihat-lihat dahulu',
    label2: 'Khidmat Pelanggan Dalam Talian',
    label3: 'Daftar Pengguna Baru',
    label4: 'Kembali ke Log Masuk',
    label5: 'Daftar',
    label6: 'Ingat akaun/kata laluan',
    placeholder1: 'Nombor Telephone Bimbit',
    placeholder2: 'Kata Laluan',
    placeholder3: 'Kod Pengesahan',
    placeholder4: 'Nombor Telephone Bimbit',
    placeholder5: 'Kata Laluan Log Masuk',
    placeholder6: 'Sahkan Kata Laluan',
    placeholder7: 'Nama Sebenar',
    placeholder8: 'Kata Laluan Pembayaran',
    placeholder9: 'Kod Pengesahan',
    placeholder10: 'Kod Jemputan',
    toast1: 'Panjang Nombor Telephone Bimbit 6~16 aksara, kombinasi huruf atau nombor!',
    toast2: 'Sila masukkan panjang kata laluan yang betul, minimum 6 aksara!',
    toast3: 'Kata laluan tidak sepadan!',
    toast4: 'Sila masukkan nama sebenar anda!',
    toast5: 'Sila masukkan panjang kata laluan pembayaran yang betul, minimum 6 aksara!',
    toast6: 'Sila masukkan kod pengesahan!',
    toast7: 'Kod pengesahan salah!',
    toast8: 'Sila masukkan akaun dan kata laluan anda!',
    toast9: 'Sila masukkan kod jemputan!',
  },

  message: {
    title: 'Pusat Mesej',
    label1: 'Pengumuman',
    label2: 'Mesej Dalaman',
    label3: 'Tiada lagi',
  },

  money: {
    title: 'Dompet Saya',
    label1: 'Jumlah Aset',
    label2: 'Dompet Pusat',
    label3: 'Dompet Pusat',
    label4: 'Dompet Permainan',
    label5: 'Deposit',
    label6: 'Pindahan',
    label7: 'Pengeluaran',
    label8: 'Akaun Bank',
    label9: 'Baki Stadium',
    label10: 'Pulihkan Semua',
    label11: 'Segarkan',
  },

  password: {
    title1: 'Ubah Kata Laluan Log Masuk',
    title2: 'Tetapkan Kata Laluan Pengeluaran',
    label1: 'Kata Laluan Asal',
    label2: 'Kata Laluan Baru',
    label3: 'Sahkan Kata Laluan Baru',
    label4: 'Sahkan Perubahan',
    placeholder1: 'Sila masukkan kata laluan semasa',
    placeholder2: 'Sila masukkan kata laluan baru',
    placeholder3: 'Sila masukkan kata laluan sekali lagi',
    toast1: 'Sila masukkan kata laluan lama',
    toast2: 'Sila masukkan kata laluan baru',
    toast3: 'Sila masukkan panjang kata laluan lama yang betul',
    toast4: 'Sila masukkan panjang kata laluan baru yang betul',
    toast5: 'Sila masukkan kata laluan pengesahan',
    toast6: 'Kata laluan tidak sepadan!',
    toast7: 'Kata laluan lama dan baru tidak boleh sama!',
    toast8: 'Kata laluan berjaya diubah!',
  },

  payInfo: {
    label1: 'Maklumat Cas',
    label2: 'RM',
    label3: 'Sila selesaikan pembayaran dalam',
    label4: '',
    label5: 'Selepas pembayaran berjaya, akan dikreditkan secara automatik!',
    label6: 'Jika ada masalah, sila',
    label7: 'Hubungi Khidmat Pelanggan',
    label8: 'Sahkan',
    label9: 'Alamat Penerima',
    label10: 'Copy',
    label11: 'Nombor Pesanan',
    label12: 'Masa Transaksi',
    label13: 'Kaedah Cas',
    label14: 'Protokol Dompet',
    label15: 'Cas Selesai',
    label16: 'Butiran Dana',
    label17: 'Berjaya disalin!',
  },

  recharge: {
    title: 'Deposit',
    label1: 'Bank Account',
    label2: 'WeChat',
    label3: 'Alipay',
    label4: 'Akaun Penerima',
    label5: 'Copy',
    label6: 'Name',
    label7: 'Bank',
    label8: 'Alamat Bank',
    label9: 'Bank Pembukaan Akaun',
    label10: 'Pemegang Akaun',
    label11: 'Sila pastikan anda memasukkan nama pemegang akaun yang betul.',
    label12: 'Nombor Kad Bank',
    label13: 'Alamat Cawangan Bank',
    label14: 'Jumlah Deposit',
    label15: 'RM',
    label16: 'Harga USDT stabil, kecairan tinggi, tidak dikawal selia',
    label17: 'Ketahui lebih lanjut',
    label18: 'Ikat Alamat Protokol',
    label19: 'Pemindahan Pertukaran',
    label20: 'Selesai Pengeluaran',
    label21: 'Protokol Dompet',
    label22: 'Jumlah Deposit',
    label23: 'Kadar Pertukaran Rujukan',
    label24: 'Peringatan Mesra',
    label25: 'Sila pilih protokol USDT yang betul untuk pembayaran. Jika anda memilih protokol pembayaran yang salah, platform tidak akan dapat menerima pembayaran anda dan kami tidak akan bertanggungjawab atas perkara ini!',
    label26: 'Jumlah Deposit',
    label27: 'Deposit Sekarang',
    label28: 'Menghadapi masalah deposit? Hubungi',
    label29: 'Khidmat Pelanggan Manusia',
    label30: 'untuk penyelesaian',
    label31: 'Peringatan Mesra',
    label32: 'Anda belum mengikat mana-mana kad dompet, sila pergi untuk mengikat!',
    label33: 'Pergi untuk Mengikat',
    label34: 'Sila masukkan jumlah antara',
    label35: '!',
    label36: 'Bank Account',
    label37: 'Sila copy maklumat bank berikut dan lengkapkan pemindahan.',
    label38: 'Payment Receipt',
    label39: 'Peringatan: ',
    label40: 'Minimum Top-up ialah RM500',
    label41: 'Minimum Top-up ialah USDT100',
    placeholder1: 'Pilih bank pembukaan akaun',
    placeholder2: 'Sila masukkan nama pemegang akaun.',
    placeholder3: 'Sila masukkan nombor kad bank',
    placeholder4: 'Sila masukkan alamat cawangan bank',
    placeholder5: 'Sila masukkan jumlah pengeluaran',
    placeholder6: 'Sila masukkan alamat USDT',
    placeholder7: 'Sila masukkan jumlah pengeluaran',
    placeholder8: 'Sila masukkan jumlah pengeluaran',
    placeholder9: 'Sila upload payment receipt',
    toast1: 'Sila masukkan nama pendeposit',
    toast2: 'Sila masukkan jenis bank',
    toast3: 'Sila masukkan nombor kad bank',
    toast4: 'Sila masukkan alamat cawangan bank',
    toast5: 'Penyerahan berjaya, menunggu semakan backend',
    toast6: 'Berjaya disalin!',
    other1: 'Alamat Dompet',
    other2: 'Catatan',
    other3: 'Sila masukkan catatan',
  },

  register: {
    toast1: 'Sila baca dan setuju dengan terma dan dasar privasi yang berkaitan!',
    toast2: 'Panjang nama pengguna 6~16 aksara, kombinasi huruf atau nombor!',
    toast3: 'Sila masukkan panjang kata laluan yang betul, minimum 6 aksara!',
    toast4: 'Kata laluan tidak sepadan!',
    toast5: 'Sila masukkan nama sebenar anda!',
    toast6: 'Sila masukkan panjang kata laluan pembayaran yang betul, minimum 6 aksara!',
  },


  transfer: {
    title: 'Pindahan',
    label1: 'Jumlah Dompet',
    label2: 'Pulihkan Semua',
    label3: 'Dompet Pusat',
    label4: 'Dompet Permainan',
    label5: 'Tutup',
    label6: 'Buka',
    label7: 'Pindahan Automatik',
    label8: 'Selepas diaktifkan, baki akan dipindahkan secara automatik ke stadium permainan',
    label9: 'Dompet dalam stadium tidak menyokong pindahan antara satu sama lain',
    label10: 'Jumlah Maksimum',
    label11: 'Pindah Sekarang',
    label12: 'Menghadapi masalah pemindahan? Hubungi',
    label13: 'Khidmat Pelanggan Manusia',
    label14: 'untuk selesaikan',
    label15: 'Pilih Dompet',
    label16: 'Dompet Platform',
    toast1: 'Sila masukkan jumlah operasi!',
    toast2: 'Dompet dalam stadium tidak menyokong pindahan antara satu sama lain',
    toast3: 'Sila masukkan jumlah operasi!',
    toast4: 'Operasi berjaya!',
    placeholder1: 'Sila masukkan jumlah pindahan',
  },

  transRecord: {
    title: 'Rekod Transaksi',
    label1: 'Tiada lagi',
    label2: 'Nombor Pesanan:',
    label3: 'Jumlah',
    label4: 'Kosong',
    label5: 'Hari Ini',
    label6: '7 Hari Lepas',
    label7: '15 Hari Lepas',
    label8: 'Semua',
    label9: 'Deposit',
    label10: 'Pengeluaran',
    label11: 'Pindah Masuk',
    label12: 'Pindah Keluar',
    label13: 'Seluruh Platform',
    label14: 'Tidak Ditentukan',
    label15: 'Menunggu Semakan',
    label16: 'Lulus Semakan',
    label17: 'Tolak Semakan',
    label18: 'Gagal',
    label19: 'Berjaya',
    label20: 'Menunggu Penyelesaian',
    label21: 'Tidak Ditentukan',
    label22: 'Batal',
    label23: 'Telah Dibatalkan',
    label24: 'Rekod Cas',
    label25: 'Rekod Pengeluaran'
  },

  usdtmore: {
    title: 'Perbezaan Protokol',
    label1: 'Pengenalan Protokol',
    label2: 'TRC20: Berdasarkan Protokol Rangkaian Tron',
    label3: 'Yuran pengeluaran TRC20 adalah yang terendah, ini bermakna pengguna boleh menikmati perkhidmatan pengeluaran pertukaran dengan yuran rendah. Pada masa yang sama, TPS rangkaian Tron boleh mencapai tahap ribuan, membolehkan pengesahan transaksi dalam beberapa saat.',
    label4: 'ERC20: Berdasarkan Protokol Rangkaian Ethereum',
    label5: 'Ethereum mengeluarkan token asli mereka sendiri dan token lain. Namun, peraturan untuk beribu-ribu jenis token adalah berbeza-beza, yang tidak baik untuk perkembangan pasaran di masa depan. Oleh itu, penerbit token telah membuat standard kontrak pintar, iaitu ERC20.',
    label6: 'Perbezaan Protokol?',
    label7: 'Perbezaan',
    label8: 'Protokol TRC20',
    label9: 'Protokol ERC20',
    label10: 'Gaya Alamat',
    label11: 'Alamat USDT bermula dengan T',
    label12: 'Alamat USDT bermula dengan 0x',
    label13: 'Rangkaian Yang Digunakan',
    label14: 'Rangkaian Tron',
    label15: 'Rangkaian Ethereum',
    label16: 'Status Rangkaian',
    label17: 'Hampir tidak sesak',
    label18: 'Sering sesak',
    label19: 'Kelajuan Pemindahan',
    label20: 'Sangat cepat',
    label21: 'Beberapa saat hingga beberapa minit',
    label22: 'Beberapa minit hingga puluhan minit',
    label23: 'Yuran Pemindahan',
    label24: 'Rendah',
    label25: 'Biasa',
    label26: 'Tahap Keselamatan',
    label27: 'Biasa',
    label28: 'Tinggi',
    label29: 'Cadangan Penggunaan',
    label30: 'Transaksi kecil dan frekuensi tinggi',
    label31: 'Cadangan Transaksi',
    label32: 'Jumlah sederhana',
    label33: 'Cadangan transaksi biasa',
    label34: 'Mana satu protokol yang lebih baik?',
    label35: 'Cadangan untuk transaksi kecil',
    label36: 'Yuran rendah, pemindahan dalam masa sesaat.',
    label37: 'Cadangan untuk jumlah sederhana',
    label38: 'Yuran dan kelajuan berada di antara nilai sederhana.',
    label39: 'Alamat USDT yang digunakan oleh dua protokol ini tidak dapat digunakan antara satu sama lain. Pastikan alamat yang betul semasa memindahkan atau mendepositkan!',
    label40: 'Biasa',
  },

  vip: {
    title: 'Keistimewaan VIP',
    label1: 'Jumlah Deposit Kumulatif',
    label2: 'Keperluan Perbelanjaan',
    label3: 'Peratus Pulangan Teratas VIP',
    label4: 'Tahap VIP',
    label5: 'Perdagangan Sebenar',
    label6: 'Sukan',
    label7: 'E-sukan',
    label8: 'Permainan Kad',
    label9: 'Permainan Elektronik',
    label10: 'Loteri',
    label11: 'Peraturan Aktiviti',
    label12: 'Standard Kenaikan',
    label13: 'Jika deposit kumulatif dan jumlah perbelanjaan mencapai tahap yang ditetapkan, anda boleh naik ke tahap VIP seterusnya pada pukul 24:00 hari berikutnya.',
    label14: 'Urutan Kenaikan',
    label15: 'VIP boleh dinaikkan satu tahap setiap hari apabila memenuhi keperluan tahap, tetapi tidak boleh dinaikkan lebih daripada satu tahap.',
    label16: 'Keperluan Kekal',
    label17: 'Setelah mencapai tahap VIP tertentu, ahli perlu memenuhi keperluan perbelanjaan dalam masa 90 hari. Sekiranya terdapat kenaikan dalam tempoh ini, keperluan kekal akan dikira semula mengikut tahap semasa.',
    label18: 'Standard Penurunan',
    label19: 'Jika ahli tidak memenuhi keperluan perbelanjaan untuk mengekalkan tahap VIP dalam tempoh 90 hari, sistem akan menurunkan tahap VIP mereka, dan semua faedah seperti pulangan dan diskaun akan disesuaikan mengikut tahap baru.',
    label20: 'Pulangan VIP',
    label21: 'Jumlah pulangan VIP dikira berdasarkan jumlah pertaruhan yang sah antara 00:00–23:59 Beijing pada hari yang sama. Pulangan akan dihantar dalam masa 24 jam selepas penyelesaian taruhan pada hari tersebut dan boleh dituntut melalui Pusat Kesejahteraan di pusat peribadi ahli (Syarat pengeluaran: pertaruhan 1x).',
    label22: 'Opal Entertainment berhak untuk mengubah, menghentikan, atau memberikan penjelasan terakhir mengenai aktiviti ini.',
    label23: 'Telah Mencapai',
    label24: 'Belum Mencapai',
  },

  userCent: {
    title: 'Maklumat Peribadi',
    label1: 'Lengkapkan maklumat akaun untuk keselamatan yang lebih tinggi',
    label2: 'Lengkapkan profil peribadi',
    label3: 'Maklumat yang lebih lengkap, perkhidmatan kami lebih menyeluruh',
    label4: 'Lengkapkan sekarang',
    label5: 'Akaun Bank',
    label6: 'Untuk pengeluaran, sila ikatkan akaun bank atau alamat mata wang digital',
    label7: 'Pengurusan Kata Laluan Log Masuk',
    label8: 'Sentiasa tukar kata laluan log masuk untuk keselamatan akaun',
    label9: 'Pengurusan Kata Laluan Pengeluaran',
    label10: 'Sentiasa tukar kata laluan pengeluaran untuk keselamatan akaun',
  },

  userInfo: {
    title: 'Maklumat Peribadi',
    label1: 'Foto Profil',
    label2: 'Nama Pengguna',
    label3: 'Nama Penuh',
    label4: 'Tarikh Lahir',
    label5: 'Nombor Telefon',
    label6: 'Emel',
    label7: 'Sahkan Pengubahsuaian',
    placeholder1: 'Sila masukkan nama pengguna',
    placeholder2: 'Sila masukkan nama penuh',
    placeholder3: 'Sila pilih tarikh lahir',
    placeholder4: 'Ikatkan nombor telefon untuk keselamatan akaun',
    placeholder5: 'Ikatkan emel untuk keselamatan akaun',
    toast1: 'Sila masukkan nombor telefon yang betul',
    toast2: 'Sila masukkan emel yang betul',
    toast3: 'Sila masukkan format tarikh yang betul: YYYY-MM-DD',
    toast4: 'Tindakan berjaya',
  },

  wallet: {
    title: 'Akaun Bank',
    label1: 'Mata Wang Digital',
    label2: 'Akaun Bank',
    label3: 'Tambah Alamat USDT',
    label4: 'Maksimum sokongan penambahan 5 alamat',
    label5: 'Tambah Akaun Bank',
    label6: 'Maksimum sokongan penambahan 5 akaun bank',
    toast1: 'Perhatian',
    toast2: 'Adakah anda pasti mahu membatalkan ikatan kad ini?',
    toast3: 'Pembatalan berjaya',
  },

  welfare: {
    title: 'Pusat Kesejahteraan',
    label1: 'Rekod Wang Redenasi',
    label2: 'Pergi untuk menuntut',
    label3: 'Bilangan penuntutan yang tinggal:',
    label4: 'Bilangan penuntutan yang telah dilakukan:',
    label5: 'Jumlah Deposit',
    label6: 'Jumlah Redenasi:',
    label7: 'Masa Deposit',
    label8: 'Masa Penuntutan:',
    label9: 'Kosong',
    label10: 'Tiada lagi',
  },

  withdrawal: {
    title: 'Pengeluaran',
    label1: 'Pengeluaran USDT',
    label2: 'Pengeluaran Akaun Bank',
    label3: 'Jumlah Wang Dalam Dompet',
    label4: 'Pengumpulan Automatik',
    label5: 'Dompet Pusat',
    label6: 'Dompet Permainan',
    label7: 'Sembunyikan',
    label8: 'Kembangkan',
    label9: 'Pilih Alamat USDT',
    label10: 'Tambah Alamat USDT',
    label11: 'Jumlah Penarikan',
    label12: 'Jumlah Pengeluaran',
    label13: 'Jumlah Maksimum',
    label14: 'Kata Laluan Pembayaran',
    label15: 'Setiap Yuran Pemindahan',
    label16: 'Bertukar kepada USDT',
    label17: 'Kadar Pertukaran:',
    label18: 'Berubah secara langsung',
    label19: 'Jumlah Yang Diterima:',
    label20: 'Pilih Akaun Bank',
    label21: 'Tambah Akaun Bank',
    label22: 'Sila pilih akaun bank',
    label23: 'Sila pilih alamat USDT',
    label24: 'Keluarkan Sekarang',
    label25: 'Masalah dengan pengeluaran? Hubungi',
    label26: 'Khidmat Pelanggan',
    label27: 'Selesaikan',
    label28: 'Pengeluaran Komisyen',
    label29: 'Peringatan: ',
    label30: 'Minimum pengeluaran ialah RM50',
    placeholder1: 'Jumlah taruhan',
    placeholder2: 'Sila masukkan jumlah pengeluaran',
    placeholder3: 'Sila masukkan kata laluan pembayaran',
    toast1: 'Sila pilih akaun bank untuk pengeluaran',
    toast2: 'Pengeluaran tidak boleh kurang daripada 50 ringgit untuk setiap transaksi',
    toast3: 'Sila masukkan kata laluan pembayaran anda',
    toast4: 'Penyerahan berjaya, menunggu kelulusan admin',
    toast5: 'Sila pilih alamat USDT',
    toast6: 'Pengeluaran tidak boleh kurang daripada 10 USDT',
    toast7: 'Sila masukkan kata laluan pembayaran anda',
    toast8: 'Penyerahan berjaya, menunggu kelulusan admin',
    errorTitle: 'Peringatan',
    errorContent: 'Untuk bekerjasama dengan usaha pencegahan pengubahan wang haram kerajaan Malaysia, saluran mata wang kripto (USDT) ditutup buat sementara waktu dan waktu pembukaan akan dimaklumkan secara berasingan.'
  },

  lottery: {
    label1: 'Baki',
    label2: 'Bilangan Sesi',
    label3: 'Tamatkan Sesi',
    label4: 'Kosongkan',
    label5: 'Pasang Sekarang',
    label6: 'Nombor Semasa',
    label7: 'Jumlah Taruhan',
    label8: 'Kemenangan Maksimum',
    label9: 'Jumlah',
    label10: 'Bilangan Sesi',
    label11: 'Nombor Pemenang',
    label12: 'Jumlah Nilai',
    label13: 'Jarak',
    label14: 'Kadar Pembayaran',
    label15: 'Taruhan',
    label16: 'Tiada lagi',
    toast1: 'Sila pilih nilai taruhan',
    toast2: 'Sila masukkan jumlah taruhan',
    toast3: 'Waktu taruhan untuk sesi ini telah tamat',
    pk10Type1: 'Tempat pertama hingga tempat kesepuluh',
    pk10Type2: 'Juara + Naib Juara = Nombor Menang',

  },

  dealRecord: {
    label1: 'Rekod Perubahan Akaun',
    label111: 'Rekod Perubahan Komisyen',
    label2: 'Akaun Saya',
    label3: 'Akaun Bawah',
    label4: 'Jumlah',
    label5: 'Baki Tersedia',
    label6: 'Sahkan',
    label7: 'Batal',
    label8: 'Sila pilih masa mula',
    label9: 'Sila pilih masa tamat',
    label10: 'Keseluruhan',
    label11: 'Orang',
    toast1: 'Masa mula dan tamat perlu berada dalam bulan yang sama',
    toast2: 'Masa mula tidak boleh melebihi masa tamat',
  },

  coinTrans: {
    label1: 'Pertukaran Mata Wang',
    label2: 'MYR ke USDT',
    label3: 'USDT ke MYR',
    label4: 'Sila masukkan jumlah pertukaran',
    label5: 'Sila masukkan kata laluan dana',
  },

  agent: {
    label1: 'Pusat Ejen',
    label2: 'Ringkasan',
    label3: 'Kod Jemputan',
    label4: 'Laporan',
    label5: 'Jumlah Anggota',
    label6: 'Jumlah Ejen',
    label7: 'Jumlah Pemain',
    label8: 'Baki Pasukan',
    label9: 'Sila masukkan kod jemputan',
    label10: 'Isi Semula',
    label11: 'Pengeluaran',
  },

  trans: {
    label1: 'Pemindahan Akaun',
    label2: 'Sila masukkan akaun bawah',
    label3: 'Sila masukkan jumlah pemindahan',
    label4: 'Sila masukkan kata laluan dana',
  },

  betList: {
    label1: 'Senarai Taruhan',
    label2: 'Sesi',
    label3: 'Kandungan Taruhan',
    label4: 'Pembelian Berjaya',
    label5: 'Menang',
    label6: 'Tidak Menang',
    label7: 'Belum Diumumkan',
    label16: 'Betting order number',
    label17: 'Game name',
    label18: 'Game play',
    label19: 'Betting time',
    label20: 'Game account',
    label21: 'Betting period number',
    label22: 'Betting number',
    label23: 'Betting amount',
    label24: 'Winning number',
    label25: 'Order status',
    label28: 'Lottery details',
    label29: 'Won',
    label30: 'Not won',
    label31: 'Not drawn',
    label32: 'Winning amount',
    label33: 'Period',
  },
}


export const other = {
  label1: 'Peringatan',
  label2: 'Adakah anda pasti ingin log keluar?',
  label3: 'Sila log masuk!',
  label4: 'Penyalinan berjaya!',
  label5: 'Kandungan menarik menanti, cepat log masuk!',
  label6: 'Redenasi telah ditutup',
  label7: 'Sudah berada di halaman ini!',
  label8: 'Pengesahan gagal',
  label9: 'Sesi log masuk anda telah tamat, sila log masuk semula',
}

export const yingkui = {
  label1: 'Keuntungan dan Kerugian',
  label2: 'Hari Ini',
  label3: 'Semalam',
  label4: 'Hari Sebelumnya',
  label5: 'Tujuh Hari Lepas',
  label6: 'Lima Belas Hari Lepas',
  label7: 'Keuntungan',
  label8: 'Jumlah Taruhan',
  label9: 'Jumlah Kemenangan',
  label10: 'Keuntungan Semalam',
  label11: 'Sila pilih bulan',
}

export const kefu = {
  tip1: 'Klik pusat khidmat pelanggan yang anda pilih untuk segera menghubungi.',
  tip2: 'Jika anda mempunyai sebarang pertanyaan, anda boleh mendapatkan perkhidmatan perundingan 1-1 melalui Telegram atau WhatsApp. Klik pada ID sembang untuk disambungkan secara automatik kepada kakitangan perkhidmatan pelanggan dan dapatkan bantuan',
  tip3: '* Maklum balas mungkin tertunda semasa waktu rehat.',
  error: 'Maaf, perisian ini belum menyokong perkhidmatan di rantau ini lagi.'
}


